"""
增强的Shapley值存储管理器 (Enhanced Shapley Storage Manager)

本模块实现了改进的Shapley值计算数据存储和组织系统，提供：
1. 周期性数据组织和管理
2. 联盟实验状态跟踪
3. 执行进度监控
4. 结果汇总和分析
5. 与OPRO系统的无缝集成

主要改进：
- 按周期（week_1, week_2等）组织数据
- 系统化的联盟实验跟踪
- 完整的执行状态监控
- 清晰的数据存储结构

存储结构：
data/trading/shapley_analysis/
├── week_1/
│   ├── coalition_NAA_TAA_TRA/
│   │   ├── coalition_info.json
│   │   ├── trading_simulation.json
│   │   ├── performance_metrics.json
│   │   ├── agent_decisions.json
│   │   └── execution_logs.json
│   ├── week_summary.json
│   ├── shapley_results.json
│   └── experiment_status.json
├── week_2/
└── analysis_summary.json
"""

import json
import logging
import os
import time
from typing import Dict, List, Any, Optional, Set, Union, Tuple
from datetime import datetime, timedelta
from pathlib import Path
from enum import Enum
import hashlib

class ExperimentStatus(Enum):
    """实验状态枚举"""
    NOT_STARTED = "NOT_STARTED"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    CANCELLED = "CANCELLED"

class EnhancedShapleyStorageManager:
    """
    增强的Shapley值存储管理器
    
    负责管理周期性Shapley值计算的数据存储，包括：
    - 周期性数据组织和管理
    - 联盟实验状态跟踪
    - 执行进度监控
    - 结果汇总和分析
    - 与OPRO系统集成
    """
    
    def __init__(self, 
                 base_data_dir: str = "data/trading",
                 config: Optional[Dict[str, Any]] = None,
                 logger: Optional[logging.Logger] = None):
        """
        初始化增强的Shapley存储管理器
        
        参数:
            base_data_dir: 数据存储基础目录
            config: 配置字典
            logger: 日志记录器
        """
        self.base_data_dir = Path(base_data_dir)
        self.config = config or {}
        self.logger = logger or logging.getLogger(__name__)
        
        # 创建Shapley分析根目录
        self.shapley_analysis_dir = self.base_data_dir / "shapley_analysis"
        self.shapley_analysis_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置选项
        self.max_concurrent_experiments = self.config.get("max_concurrent_experiments", 4)
        self.experiment_timeout = self.config.get("experiment_timeout", 300)  # 5分钟
        self.auto_retry_failed = self.config.get("auto_retry_failed", True)
        self.max_retry_attempts = self.config.get("max_retry_attempts", 3)
        self.retention_weeks = self.config.get("retention_weeks", 12)
        
        # 当前活动状态
        self.current_week_number = None
        self.current_week_dir = None
        self.active_experiments = {}  # coalition -> status
        
        self.logger.info(f"增强的Shapley存储管理器初始化完成，分析目录: {self.shapley_analysis_dir}")
    
    def start_weekly_shapley_analysis(self, 
                                    week_number: int,
                                    week_config: Optional[Dict[str, Any]] = None) -> str:
        """
        开始新的周期Shapley分析
        
        参数:
            week_number: 周期编号
            week_config: 周期配置信息
            
        返回:
            周期分析ID
        """
        week_id = f"week_{week_number}"
        self.current_week_number = week_number
        self.current_week_dir = self.shapley_analysis_dir / week_id
        
        # 创建周期目录
        self.current_week_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化周期信息
        week_info = {
            "week_id": week_id,
            "week_number": week_number,
            "start_time": datetime.now().isoformat(),
            "status": "STARTED",
            "config": week_config or {},
            "coalitions": {},
            "experiment_summary": {
                "total_coalitions": 0,
                "completed_coalitions": 0,
                "failed_coalitions": 0,
                "pending_coalitions": 0
            }
        }
        
        # 保存周期信息
        week_info_file = self.current_week_dir / "week_info.json"
        with open(week_info_file, 'w', encoding='utf-8') as f:
            json.dump(week_info, f, ensure_ascii=False, indent=2)
        
        # 初始化实验状态跟踪
        self._initialize_experiment_status_tracking()
        
        self.logger.info(f"开始周期Shapley分析: {week_id}")
        return week_id
    
    def initialize_week_experiments(self, 
                                  week_number: int,
                                  coalitions: List[Set[str]],
                                  experiment_config: Optional[Dict[str, Any]] = None) -> None:
        """
        初始化周期实验
        
        参数:
            week_number: 周期编号
            coalitions: 联盟列表
            experiment_config: 实验配置
        """
        if self.current_week_number != week_number:
            self.start_weekly_shapley_analysis(week_number)
        
        # 重置活动实验状态
        self.active_experiments = {}
        
        # 为每个联盟创建存储结构并初始化状态
        for coalition in coalitions:
            coalition_name = self._generate_coalition_name(coalition)
            
            # 创建联盟目录
            coalition_dir = self._create_coalition_storage(coalition, experiment_config or {})
            
            # 初始化实验状态
            self.active_experiments[coalition_name] = {
                "coalition": coalition,
                "status": ExperimentStatus.NOT_STARTED,
                "start_time": None,
                "end_time": None,
                "retry_count": 0,
                "error_message": None,
                "coalition_dir": coalition_dir
            }
        
        # 更新实验状态文件
        self._update_experiment_status_file()
        
        # 更新周期汇总
        self._update_week_summary({
            "total_coalitions": len(coalitions),
            "initialized_time": datetime.now().isoformat()
        })
        
        self.logger.info(f"初始化周期 {week_number} 的 {len(coalitions)} 个联盟实验")
    
    def track_coalition_experiment(self, 
                                 coalition: Union[Set[str], str],
                                 status: Union[ExperimentStatus, str],
                                 error_message: Optional[str] = None) -> None:
        """
        跟踪联盟实验状态
        
        参数:
            coalition: 联盟成员集合或联盟名称
            status: 实验状态
            error_message: 错误信息（如果有）
        """
        if isinstance(coalition, set):
            coalition_name = self._generate_coalition_name(coalition)
        else:
            coalition_name = coalition
        
        if isinstance(status, str):
            status = ExperimentStatus(status)
        
        if coalition_name not in self.active_experiments:
            self.logger.warning(f"联盟 {coalition_name} 未在活动实验中找到")
            return
        
        # 更新实验状态
        experiment = self.active_experiments[coalition_name]
        old_status = experiment["status"]
        experiment["status"] = status
        
        # 更新时间戳
        current_time = datetime.now().isoformat()
        if status == ExperimentStatus.IN_PROGRESS and old_status == ExperimentStatus.NOT_STARTED:
            experiment["start_time"] = current_time
        elif status in [ExperimentStatus.COMPLETED, ExperimentStatus.FAILED, ExperimentStatus.CANCELLED]:
            experiment["end_time"] = current_time
        
        # 处理错误信息
        if error_message:
            experiment["error_message"] = error_message
        
        # 处理失败重试
        if status == ExperimentStatus.FAILED and self.auto_retry_failed:
            if experiment["retry_count"] < self.max_retry_attempts:
                experiment["retry_count"] += 1
                experiment["status"] = ExperimentStatus.NOT_STARTED
                self.logger.info(f"联盟 {coalition_name} 实验失败，准备第 {experiment['retry_count']} 次重试")
            else:
                self.logger.error(f"联盟 {coalition_name} 实验失败，已达到最大重试次数")
        
        # 更新状态文件
        self._update_experiment_status_file()
        
        # 记录状态变化
        self.logger.info(f"联盟 {coalition_name} 状态更新: {old_status.value} -> {status.value}")
    
    def save_coalition_experiment_result(self,
                                       coalition: Union[Set[str], str],
                                       result: Dict[str, Any],
                                       agent_decisions: Optional[Dict[str, Any]] = None,
                                       execution_logs: Optional[List[Dict[str, Any]]] = None) -> None:
        """
        保存联盟实验结果
        
        参数:
            coalition: 联盟成员集合或联盟名称
            result: 实验结果数据
            agent_decisions: 智能体决策数据
            execution_logs: 执行日志
        """
        if isinstance(coalition, set):
            coalition_name = self._generate_coalition_name(coalition)
            coalition_set = coalition
        else:
            coalition_name = coalition
            coalition_set = self.active_experiments[coalition_name]["coalition"]
        
        if coalition_name not in self.active_experiments:
            self.logger.error(f"联盟 {coalition_name} 未在活动实验中找到")
            return
        
        coalition_dir = self.active_experiments[coalition_name]["coalition_dir"]
        
        # 保存交易模拟结果
        simulation_file = coalition_dir / "trading_simulation.json"
        with open(simulation_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        # 提取并保存性能指标
        performance_metrics = self._extract_performance_metrics(result)
        performance_file = coalition_dir / "performance_metrics.json"
        with open(performance_file, 'w', encoding='utf-8') as f:
            json.dump(performance_metrics, f, ensure_ascii=False, indent=2)
        
        # 保存智能体决策数据
        if agent_decisions:
            decisions_file = coalition_dir / "agent_decisions.json"
            with open(decisions_file, 'w', encoding='utf-8') as f:
                json.dump(agent_decisions, f, ensure_ascii=False, indent=2)
        
        # 保存执行日志
        if execution_logs:
            logs_file = coalition_dir / "execution_logs.json"
            with open(logs_file, 'w', encoding='utf-8') as f:
                json.dump(execution_logs, f, ensure_ascii=False, indent=2)
        
        # 更新联盟状态为完成
        self.track_coalition_experiment(coalition_name, ExperimentStatus.COMPLETED)
        
        self.logger.info(f"保存联盟 {coalition_name} 实验结果")
    
    def get_week_experiment_status(self, week_number: Optional[int] = None) -> Dict[str, Any]:
        """
        获取周期实验状态
        
        参数:
            week_number: 周期编号，如果为None则使用当前周期
            
        返回:
            实验状态字典
        """
        if week_number is None:
            week_number = self.current_week_number
        
        if week_number is None:
            return {"error": "未指定周期编号且无当前活动周期"}
        
        week_dir = self.shapley_analysis_dir / f"week_{week_number}"
        if not week_dir.exists():
            return {"error": f"周期 {week_number} 不存在"}
        
        # 加载实验状态
        status_file = week_dir / "experiment_status.json"
        if status_file.exists():
            with open(status_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        
        return {"error": "实验状态文件不存在"}
    
    def is_week_analysis_complete(self, week_number: Optional[int] = None) -> bool:
        """
        检查周期分析是否完成
        
        参数:
            week_number: 周期编号
            
        返回:
            是否完成
        """
        status = self.get_week_experiment_status(week_number)
        if "error" in status:
            return False
        
        summary = status.get("experiment_summary", {})
        total = summary.get("total_coalitions", 0)
        completed = summary.get("completed_coalitions", 0)
        
        return total > 0 and completed == total

    def generate_weekly_summary(self, week_number: Optional[int] = None) -> Dict[str, Any]:
        """
        生成周期汇总报告

        参数:
            week_number: 周期编号

        返回:
            周期汇总字典
        """
        if week_number is None:
            week_number = self.current_week_number

        if week_number is None:
            return {"error": "未指定周期编号且无当前活动周期"}

        week_dir = self.shapley_analysis_dir / f"week_{week_number}"
        if not week_dir.exists():
            return {"error": f"周期 {week_number} 不存在"}

        # 收集所有联盟的性能数据
        coalitions_performance = {}
        coalition_dirs = [d for d in week_dir.iterdir() if d.is_dir() and d.name.startswith("coalition_")]

        for coalition_dir in coalition_dirs:
            coalition_name = coalition_dir.name

            # 加载性能指标
            performance_file = coalition_dir / "performance_metrics.json"
            if performance_file.exists():
                with open(performance_file, 'r', encoding='utf-8') as f:
                    performance = json.load(f)
                    coalitions_performance[coalition_name] = performance

        # 计算汇总统计
        summary = {
            "week_number": week_number,
            "generation_time": datetime.now().isoformat(),
            "total_coalitions": len(coalitions_performance),
            "coalitions_performance": coalitions_performance,
            "performance_statistics": self._calculate_performance_statistics(coalitions_performance),
            "experiment_status": self.get_week_experiment_status(week_number)
        }

        # 保存汇总报告
        summary_file = week_dir / "week_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)

        self.logger.info(f"生成周期 {week_number} 汇总报告")
        return summary

    def get_pending_experiments(self, week_number: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        获取待执行的实验列表

        参数:
            week_number: 周期编号

        返回:
            待执行实验列表
        """
        if week_number is None:
            week_number = self.current_week_number

        if week_number != self.current_week_number:
            return []

        pending_experiments = []
        for coalition_name, experiment in self.active_experiments.items():
            if experiment["status"] == ExperimentStatus.NOT_STARTED:
                pending_experiments.append({
                    "coalition_name": coalition_name,
                    "coalition": list(experiment["coalition"]),
                    "retry_count": experiment["retry_count"]
                })

        return pending_experiments

    def cleanup_old_weeks(self, keep_weeks: Optional[int] = None) -> List[str]:
        """
        清理旧的周期数据

        参数:
            keep_weeks: 保留的周期数量

        返回:
            被清理的周期列表
        """
        if keep_weeks is None:
            keep_weeks = self.retention_weeks

        # 确保keep_weeks是有效的整数
        if keep_weeks is None or keep_weeks <= 0:
            return []

        # 获取所有周期目录
        week_dirs = []
        for item in self.shapley_analysis_dir.iterdir():
            if item.is_dir() and item.name.startswith("week_"):
                try:
                    week_num = int(item.name.split("_")[1])
                    week_dirs.append((week_num, item))
                except (IndexError, ValueError):
                    continue

        # 按周期编号排序
        week_dirs.sort(key=lambda x: x[0])

        # 确定需要清理的周期
        if len(week_dirs) <= keep_weeks:
            return []

        to_cleanup = week_dirs[:-keep_weeks]
        cleaned_weeks = []

        for week_num, week_dir in to_cleanup:
            try:
                # 备份重要数据（如果需要）
                self._backup_week_data(week_num, week_dir)

                # 删除目录
                import shutil
                shutil.rmtree(week_dir)
                cleaned_weeks.append(f"week_{week_num}")

                self.logger.info(f"清理旧周期数据: week_{week_num}")
            except Exception as e:
                self.logger.error(f"清理周期 {week_num} 失败: {e}")

        return cleaned_weeks

    def _generate_coalition_name(self, coalition: Set[str]) -> str:
        """生成联盟名称"""
        sorted_agents = sorted(list(coalition))
        return f"coalition_{'_'.join(sorted_agents)}"

    def _create_coalition_storage(self,
                                coalition: Set[str],
                                experiment_config: Dict[str, Any]) -> Path:
        """创建联盟存储目录"""
        if self.current_week_dir is None:
            raise ValueError("当前周期目录未设置，请先调用start_weekly_shapley_analysis")

        coalition_name = self._generate_coalition_name(coalition)
        coalition_dir = self.current_week_dir / coalition_name
        coalition_dir.mkdir(parents=True, exist_ok=True)

        # 创建联盟信息文件
        coalition_info = {
            "coalition_name": coalition_name,
            "agents": list(coalition),
            "creation_time": datetime.now().isoformat(),
            "experiment_config": experiment_config,
            "status": "INITIALIZED"
        }

        info_file = coalition_dir / "coalition_info.json"
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(coalition_info, f, ensure_ascii=False, indent=2)

        return coalition_dir

    def _initialize_experiment_status_tracking(self) -> None:
        """初始化实验状态跟踪"""
        if self.current_week_dir is None:
            raise ValueError("当前周期目录未设置，请先调用start_weekly_shapley_analysis")

        status_data = {
            "week_number": self.current_week_number,
            "initialization_time": datetime.now().isoformat(),
            "experiments": {},
            "experiment_summary": {
                "total_coalitions": 0,
                "not_started": 0,
                "in_progress": 0,
                "completed": 0,
                "failed": 0,
                "cancelled": 0
            }
        }

        status_file = self.current_week_dir / "experiment_status.json"
        with open(status_file, 'w', encoding='utf-8') as f:
            json.dump(status_data, f, ensure_ascii=False, indent=2)

    def _update_experiment_status_file(self) -> None:
        """更新实验状态文件"""
        if self.current_week_dir is None:
            return

        # 统计各状态的实验数量
        status_counts = {
            "not_started": 0,
            "in_progress": 0,
            "completed": 0,
            "failed": 0,
            "cancelled": 0
        }

        experiments_data = {}
        for coalition_name, experiment in self.active_experiments.items():
            status = experiment["status"]
            status_counts[status.value.lower()] += 1

            experiments_data[coalition_name] = {
                "coalition": list(experiment["coalition"]),
                "status": status.value,
                "start_time": experiment["start_time"],
                "end_time": experiment["end_time"],
                "retry_count": experiment["retry_count"],
                "error_message": experiment["error_message"]
            }

        status_data = {
            "week_number": self.current_week_number,
            "last_update": datetime.now().isoformat(),
            "experiments": experiments_data,
            "experiment_summary": {
                "total_coalitions": len(self.active_experiments),
                **status_counts
            }
        }

        status_file = self.current_week_dir / "experiment_status.json"
        with open(status_file, 'w', encoding='utf-8') as f:
            json.dump(status_data, f, ensure_ascii=False, indent=2)

    def _update_week_summary(self, update_data: Dict[str, Any]) -> None:
        """更新周期汇总信息"""
        if self.current_week_dir is None:
            return

        week_info_file = self.current_week_dir / "week_info.json"

        # 加载现有数据
        if week_info_file.exists():
            with open(week_info_file, 'r', encoding='utf-8') as f:
                week_info = json.load(f)
        else:
            week_info = {}

        # 更新数据
        week_info.update(update_data)
        week_info["last_update"] = datetime.now().isoformat()

        # 保存更新后的数据
        with open(week_info_file, 'w', encoding='utf-8') as f:
            json.dump(week_info, f, ensure_ascii=False, indent=2)

    def _extract_performance_metrics(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """从实验结果中提取性能指标"""
        metrics = {
            "extraction_time": datetime.now().isoformat(),
            "total_return": 0.0,
            "sharpe_ratio": 0.0,
            "max_drawdown": 0.0,
            "win_rate": 0.0,
            "total_trades": 0,
            "profitable_trades": 0,
            "average_return_per_trade": 0.0
        }

        try:
            # 从结果中提取关键指标
            if "performance" in result:
                perf = result["performance"]
                metrics.update({
                    "total_return": perf.get("total_return", 0.0),
                    "sharpe_ratio": perf.get("sharpe_ratio", 0.0),
                    "max_drawdown": perf.get("max_drawdown", 0.0),
                    "win_rate": perf.get("win_rate", 0.0)
                })

            if "trading_summary" in result:
                summary = result["trading_summary"]
                metrics.update({
                    "total_trades": summary.get("total_trades", 0),
                    "profitable_trades": summary.get("profitable_trades", 0),
                    "average_return_per_trade": summary.get("average_return_per_trade", 0.0)
                })

        except Exception as e:
            self.logger.warning(f"提取性能指标时出错: {e}")

        return metrics

    def _calculate_performance_statistics(self, coalitions_performance: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """计算性能统计信息"""
        if not coalitions_performance:
            return {}

        # 收集所有性能指标
        returns = []
        sharpe_ratios = []
        max_drawdowns = []
        win_rates = []

        for coalition_name, performance in coalitions_performance.items():
            returns.append(performance.get("total_return", 0.0))
            sharpe_ratios.append(performance.get("sharpe_ratio", 0.0))
            max_drawdowns.append(performance.get("max_drawdown", 0.0))
            win_rates.append(performance.get("win_rate", 0.0))

        # 计算统计指标
        statistics = {
            "total_return": {
                "mean": sum(returns) / len(returns) if returns else 0.0,
                "max": max(returns) if returns else 0.0,
                "min": min(returns) if returns else 0.0,
                "std": self._calculate_std(returns) if len(returns) > 1 else 0.0
            },
            "sharpe_ratio": {
                "mean": sum(sharpe_ratios) / len(sharpe_ratios) if sharpe_ratios else 0.0,
                "max": max(sharpe_ratios) if sharpe_ratios else 0.0,
                "min": min(sharpe_ratios) if sharpe_ratios else 0.0,
                "std": self._calculate_std(sharpe_ratios) if len(sharpe_ratios) > 1 else 0.0
            },
            "max_drawdown": {
                "mean": sum(max_drawdowns) / len(max_drawdowns) if max_drawdowns else 0.0,
                "max": max(max_drawdowns) if max_drawdowns else 0.0,
                "min": min(max_drawdowns) if max_drawdowns else 0.0,
                "std": self._calculate_std(max_drawdowns) if len(max_drawdowns) > 1 else 0.0
            },
            "win_rate": {
                "mean": sum(win_rates) / len(win_rates) if win_rates else 0.0,
                "max": max(win_rates) if win_rates else 0.0,
                "min": min(win_rates) if win_rates else 0.0,
                "std": self._calculate_std(win_rates) if len(win_rates) > 1 else 0.0
            }
        }

        return statistics

    def _calculate_std(self, values: List[float]) -> float:
        """计算标准差"""
        if len(values) < 2:
            return 0.0

        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / (len(values) - 1)
        return variance ** 0.5

    def _backup_week_data(self, week_num: int, week_dir: Path) -> None:
        """备份周期数据"""
        try:
            backup_dir = self.shapley_analysis_dir / "backups"
            backup_dir.mkdir(exist_ok=True)

            # 只备份重要文件
            important_files = ["week_summary.json", "shapley_results.json", "experiment_status.json"]

            week_backup_dir = backup_dir / f"week_{week_num}_backup_{int(time.time())}"
            week_backup_dir.mkdir(exist_ok=True)

            for file_name in important_files:
                source_file = week_dir / file_name
                if source_file.exists():
                    import shutil
                    shutil.copy2(source_file, week_backup_dir / file_name)

            self.logger.info(f"备份周期 {week_num} 重要数据到 {week_backup_dir}")

        except Exception as e:
            self.logger.warning(f"备份周期 {week_num} 数据失败: {e}")

    def get_analysis_summary(self) -> Dict[str, Any]:
        """获取总体分析汇总"""
        summary_file = self.shapley_analysis_dir / "analysis_summary.json"

        # 收集所有周期的数据
        weeks_data = {}
        for item in self.shapley_analysis_dir.iterdir():
            if item.is_dir() and item.name.startswith("week_"):
                try:
                    week_num = int(item.name.split("_")[1])
                    week_summary_file = item / "week_summary.json"
                    if week_summary_file.exists():
                        with open(week_summary_file, 'r', encoding='utf-8') as f:
                            weeks_data[f"week_{week_num}"] = json.load(f)
                except (IndexError, ValueError, json.JSONDecodeError):
                    continue

        # 生成总体汇总
        analysis_summary = {
            "generation_time": datetime.now().isoformat(),
            "total_weeks_analyzed": len(weeks_data),
            "weeks_data": weeks_data,
            "overall_statistics": self._calculate_overall_statistics(weeks_data)
        }

        # 保存汇总
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(analysis_summary, f, ensure_ascii=False, indent=2)

        return analysis_summary

    def _calculate_overall_statistics(self, weeks_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """计算总体统计信息"""
        if not weeks_data:
            return {}

        total_coalitions = 0
        all_performance_data = {}

        for week_id, week_data in weeks_data.items():
            coalitions_performance = week_data.get("coalitions_performance", {})
            total_coalitions += len(coalitions_performance)

            for coalition_name, performance in coalitions_performance.items():
                if coalition_name not in all_performance_data:
                    all_performance_data[coalition_name] = []
                all_performance_data[coalition_name].append(performance)

        return {
            "total_weeks": len(weeks_data),
            "total_coalition_experiments": total_coalitions,
            "unique_coalitions": len(all_performance_data),
            "average_coalitions_per_week": total_coalitions / len(weeks_data) if weeks_data else 0
        }
