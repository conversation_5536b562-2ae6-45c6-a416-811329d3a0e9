"""
联盟实验执行跟踪器 (Coalition Experiment Tracker)

本模块实现了联盟实验的执行跟踪和管理，负责：
1. 跟踪所有联盟实验状态
2. 监控实验执行进度
3. 处理实验失败和重试
4. 生成执行报告

主要功能：
- 实验状态管理和跟踪
- 并发实验控制
- 失败重试机制
- 进度监控和报告
- 资源使用优化

状态管理：
- NOT_STARTED: 未开始
- IN_PROGRESS: 执行中
- COMPLETED: 已完成
- FAILED: 失败
- CANCELLED: 已取消
"""

import json
import logging
import threading
import time
from typing import Dict, List, Any, Optional, Set, Callable
from datetime import datetime, timedelta
from pathlib import Path
from enum import Enum
from concurrent.futures import ThreadPoolExecutor, Future
import queue

class ExperimentStatus(Enum):
    """实验状态枚举"""
    NOT_STARTED = "NOT_STARTED"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    CANCELLED = "CANCELLED"

class CoalitionExperimentTracker:
    """
    联盟实验执行跟踪器
    
    负责管理和跟踪联盟实验的执行，包括：
    - 实验状态跟踪
    - 并发执行控制
    - 失败处理和重试
    - 进度监控和报告
    """
    
    def __init__(self,
                 config: Dict[str, Any],
                 logger: Optional[logging.Logger] = None):
        """
        初始化联盟实验跟踪器
        
        参数:
            config: 配置字典
            logger: 日志记录器
        """
        self.config = config
        self.logger = logger or logging.getLogger(__name__)
        
        # 配置参数
        self.max_concurrent_experiments = config.get("max_concurrent_experiments", 4)
        self.experiment_timeout = config.get("experiment_timeout", 300)  # 5分钟
        self.auto_retry_failed = config.get("auto_retry_failed", True)
        self.max_retry_attempts = config.get("max_retry_attempts", 3)
        self.retry_delay = config.get("retry_delay", 30)  # 30秒
        
        # 状态管理
        self.experiments = {}  # coalition_name -> experiment_info
        self.active_experiments = {}  # coalition_name -> Future
        self.experiment_queue = queue.Queue()
        self.executor = ThreadPoolExecutor(max_workers=self.max_concurrent_experiments)
        
        # 统计信息
        self.stats = {
            "total_experiments": 0,
            "completed_experiments": 0,
            "failed_experiments": 0,
            "cancelled_experiments": 0,
            "retry_attempts": 0
        }
        
        # 回调函数
        self.on_experiment_start = None
        self.on_experiment_complete = None
        self.on_experiment_failed = None
        
        self.logger.info("联盟实验跟踪器初始化完成")
    
    def initialize_week_experiments(self,
                                  week_number: int,
                                  coalitions: List[Set[str]],
                                  experiment_config: Optional[Dict[str, Any]] = None) -> None:
        """
        初始化周期实验
        
        参数:
            week_number: 周期编号
            coalitions: 联盟列表
            experiment_config: 实验配置
        """
        self.week_number = week_number
        self.experiments = {}
        self.active_experiments = {}
        
        # 清空队列
        while not self.experiment_queue.empty():
            try:
                self.experiment_queue.get_nowait()
            except queue.Empty:
                break
        
        # 初始化实验信息
        for coalition in coalitions:
            coalition_name = self._generate_coalition_name(coalition)
            
            self.experiments[coalition_name] = {
                "coalition": coalition,
                "coalition_name": coalition_name,
                "status": ExperimentStatus.NOT_STARTED,
                "start_time": None,
                "end_time": None,
                "duration": None,
                "retry_count": 0,
                "error_message": None,
                "result": None,
                "config": experiment_config or {}
            }
            
            # 添加到执行队列
            self.experiment_queue.put(coalition_name)
        
        # 更新统计
        self.stats["total_experiments"] = len(coalitions)
        self.stats["completed_experiments"] = 0
        self.stats["failed_experiments"] = 0
        self.stats["cancelled_experiments"] = 0
        self.stats["retry_attempts"] = 0
        
        self.logger.info(f"初始化周期 {week_number} 的 {len(coalitions)} 个联盟实验")
    
    def start_experiment_execution(self,
                                 experiment_executor: Callable[[Set[str]], Dict[str, Any]]) -> None:
        """
        开始执行实验
        
        参数:
            experiment_executor: 实验执行函数
        """
        self.experiment_executor = experiment_executor
        
        # 启动实验执行线程
        execution_thread = threading.Thread(target=self._execute_experiments_worker)
        execution_thread.daemon = True
        execution_thread.start()
        
        self.logger.info("开始执行联盟实验")
    
    def update_experiment_status(self,
                               coalition_name: str,
                               status: ExperimentStatus,
                               result: Optional[Dict[str, Any]] = None,
                               error_message: Optional[str] = None) -> None:
        """
        更新实验状态
        
        参数:
            coalition_name: 联盟名称
            status: 新状态
            result: 实验结果
            error_message: 错误信息
        """
        if coalition_name not in self.experiments:
            self.logger.warning(f"联盟 {coalition_name} 不在实验列表中")
            return
        
        experiment = self.experiments[coalition_name]
        old_status = experiment["status"]
        experiment["status"] = status
        
        current_time = datetime.now()
        
        # 更新时间戳
        if status == ExperimentStatus.IN_PROGRESS and old_status == ExperimentStatus.NOT_STARTED:
            experiment["start_time"] = current_time.isoformat()
            if self.on_experiment_start:
                self.on_experiment_start(coalition_name, experiment)
        
        elif status in [ExperimentStatus.COMPLETED, ExperimentStatus.FAILED, ExperimentStatus.CANCELLED]:
            experiment["end_time"] = current_time.isoformat()
            
            if experiment["start_time"]:
                start_time = datetime.fromisoformat(experiment["start_time"])
                experiment["duration"] = (current_time - start_time).total_seconds()
        
        # 处理结果和错误
        if result:
            experiment["result"] = result
        
        if error_message:
            experiment["error_message"] = error_message
        
        # 更新统计
        if status == ExperimentStatus.COMPLETED:
            self.stats["completed_experiments"] += 1
            if self.on_experiment_complete:
                self.on_experiment_complete(coalition_name, experiment)
        
        elif status == ExperimentStatus.FAILED:
            self.stats["failed_experiments"] += 1
            if self.on_experiment_failed:
                self.on_experiment_failed(coalition_name, experiment)
            
            # 处理重试
            if self.auto_retry_failed and experiment["retry_count"] < self.max_retry_attempts:
                self._schedule_retry(coalition_name)
        
        elif status == ExperimentStatus.CANCELLED:
            self.stats["cancelled_experiments"] += 1
        
        self.logger.info(f"实验状态更新: {coalition_name} {old_status.value} -> {status.value}")
    
    def get_experiment_status(self, coalition_name: Optional[str] = None) -> Dict[str, Any]:
        """
        获取实验状态
        
        参数:
            coalition_name: 联盟名称，如果为None则返回所有实验状态
            
        返回:
            实验状态字典
        """
        if coalition_name:
            if coalition_name in self.experiments:
                return self.experiments[coalition_name].copy()
            else:
                return {"error": f"联盟 {coalition_name} 不存在"}
        
        # 返回所有实验状态
        return {
            "week_number": getattr(self, "week_number", None),
            "timestamp": datetime.now().isoformat(),
            "experiments": {name: exp.copy() for name, exp in self.experiments.items()},
            "statistics": self.stats.copy(),
            "active_experiments": list(self.active_experiments.keys()),
            "queue_size": self.experiment_queue.qsize()
        }
    
    def get_pending_experiments(self) -> List[str]:
        """获取待执行的实验列表"""
        pending = []
        for name, experiment in self.experiments.items():
            if experiment["status"] == ExperimentStatus.NOT_STARTED:
                pending.append(name)
        return pending
    
    def get_active_experiments(self) -> List[str]:
        """获取正在执行的实验列表"""
        return list(self.active_experiments.keys())
    
    def cancel_experiment(self, coalition_name: str) -> bool:
        """
        取消实验
        
        参数:
            coalition_name: 联盟名称
            
        返回:
            是否成功取消
        """
        if coalition_name not in self.experiments:
            return False
        
        experiment = self.experiments[coalition_name]
        
        # 如果实验正在执行，尝试取消
        if coalition_name in self.active_experiments:
            future = self.active_experiments[coalition_name]
            if future.cancel():
                self.update_experiment_status(coalition_name, ExperimentStatus.CANCELLED)
                del self.active_experiments[coalition_name]
                return True
            else:
                # 如果无法取消，标记为取消状态但让其完成
                experiment["cancelled"] = True
                return False
        
        # 如果实验未开始，直接标记为取消
        elif experiment["status"] == ExperimentStatus.NOT_STARTED:
            self.update_experiment_status(coalition_name, ExperimentStatus.CANCELLED)
            return True
        
        return False
    
    def generate_execution_report(self) -> Dict[str, Any]:
        """生成执行报告"""
        current_time = datetime.now()
        
        # 计算执行统计
        durations = []
        success_rate = 0
        
        for experiment in self.experiments.values():
            if experiment["duration"]:
                durations.append(experiment["duration"])
        
        if self.stats["total_experiments"] > 0:
            success_rate = self.stats["completed_experiments"] / self.stats["total_experiments"]
        
        report = {
            "generation_time": current_time.isoformat(),
            "week_number": getattr(self, "week_number", None),
            "execution_summary": {
                "total_experiments": self.stats["total_experiments"],
                "completed": self.stats["completed_experiments"],
                "failed": self.stats["failed_experiments"],
                "cancelled": self.stats["cancelled_experiments"],
                "in_progress": len(self.active_experiments),
                "pending": len(self.get_pending_experiments()),
                "success_rate": success_rate,
                "retry_attempts": self.stats["retry_attempts"]
            },
            "performance_metrics": {
                "average_duration": sum(durations) / len(durations) if durations else 0,
                "min_duration": min(durations) if durations else 0,
                "max_duration": max(durations) if durations else 0,
                "total_execution_time": sum(durations)
            },
            "detailed_experiments": {
                name: {
                    "status": exp["status"].value,
                    "duration": exp["duration"],
                    "retry_count": exp["retry_count"],
                    "error_message": exp["error_message"]
                } for name, exp in self.experiments.items()
            }
        }
        
        return report

    def _generate_coalition_name(self, coalition: Set[str]) -> str:
        """生成联盟名称"""
        return "_".join(sorted(list(coalition)))

    def _execute_experiments_worker(self) -> None:
        """实验执行工作线程"""
        while True:
            try:
                # 检查是否有可用的执行槽位
                if len(self.active_experiments) >= self.max_concurrent_experiments:
                    time.sleep(1)
                    continue

                # 从队列获取待执行的实验
                try:
                    coalition_name = self.experiment_queue.get(timeout=1)
                except queue.Empty:
                    time.sleep(1)
                    continue

                # 检查实验是否仍然有效
                if coalition_name not in self.experiments:
                    continue

                experiment = self.experiments[coalition_name]
                if experiment["status"] != ExperimentStatus.NOT_STARTED:
                    continue

                # 提交实验执行
                future = self.executor.submit(self._execute_single_experiment, coalition_name)
                self.active_experiments[coalition_name] = future

                # 更新状态
                self.update_experiment_status(coalition_name, ExperimentStatus.IN_PROGRESS)

            except Exception as e:
                self.logger.error(f"实验执行工作线程错误: {e}")
                time.sleep(5)

    def _execute_single_experiment(self, coalition_name: str) -> Dict[str, Any]:
        """执行单个实验"""
        try:
            experiment = self.experiments[coalition_name]
            coalition = experiment["coalition"]

            # 检查是否被取消
            if experiment.get("cancelled", False):
                self.update_experiment_status(coalition_name, ExperimentStatus.CANCELLED)
                return {"status": "cancelled"}

            # 执行实验
            start_time = time.time()
            result = self.experiment_executor(coalition)
            execution_time = time.time() - start_time

            # 检查超时
            if execution_time > self.experiment_timeout:
                raise TimeoutError(f"实验执行超时: {execution_time:.2f}秒")

            # 更新成功状态
            self.update_experiment_status(
                coalition_name,
                ExperimentStatus.COMPLETED,
                result=result
            )

            return result

        except Exception as e:
            error_msg = f"实验执行失败: {str(e)}"
            self.logger.error(f"联盟 {coalition_name} {error_msg}")

            self.update_experiment_status(
                coalition_name,
                ExperimentStatus.FAILED,
                error_message=error_msg
            )

            return {"error": error_msg}

        finally:
            # 从活动实验中移除
            if coalition_name in self.active_experiments:
                del self.active_experiments[coalition_name]

    def _schedule_retry(self, coalition_name: str) -> None:
        """安排重试"""
        experiment = self.experiments[coalition_name]
        experiment["retry_count"] += 1
        self.stats["retry_attempts"] += 1

        # 延迟后重新加入队列
        def delayed_retry():
            time.sleep(self.retry_delay)
            experiment["status"] = ExperimentStatus.NOT_STARTED
            experiment["error_message"] = None
            self.experiment_queue.put(coalition_name)
            self.logger.info(f"安排重试: {coalition_name} (第{experiment['retry_count']}次)")

        retry_thread = threading.Thread(target=delayed_retry)
        retry_thread.daemon = True
        retry_thread.start()

    def wait_for_completion(self, timeout: Optional[float] = None) -> bool:
        """
        等待所有实验完成

        参数:
            timeout: 超时时间（秒）

        返回:
            是否所有实验都完成
        """
        start_time = time.time()

        while True:
            # 检查是否所有实验都完成
            all_completed = True
            for experiment in self.experiments.values():
                if experiment["status"] in [ExperimentStatus.NOT_STARTED, ExperimentStatus.IN_PROGRESS]:
                    all_completed = False
                    break

            if all_completed:
                return True

            # 检查超时
            if timeout and (time.time() - start_time) > timeout:
                return False

            time.sleep(1)

    def cleanup(self) -> None:
        """清理资源"""
        # 取消所有活动实验
        for coalition_name in list(self.active_experiments.keys()):
            self.cancel_experiment(coalition_name)

        # 关闭线程池
        self.executor.shutdown(wait=True)

        self.logger.info("联盟实验跟踪器资源清理完成")

    def set_callbacks(self,
                     on_start: Optional[Callable] = None,
                     on_complete: Optional[Callable] = None,
                     on_failed: Optional[Callable] = None) -> None:
        """
        设置回调函数

        参数:
            on_start: 实验开始回调
            on_complete: 实验完成回调
            on_failed: 实验失败回调
        """
        self.on_experiment_start = on_start
        self.on_experiment_complete = on_complete
        self.on_experiment_failed = on_failed

    def get_progress_summary(self) -> Dict[str, Any]:
        """获取进度摘要"""
        total = self.stats["total_experiments"]
        completed = self.stats["completed_experiments"]
        failed = self.stats["failed_experiments"]
        cancelled = self.stats["cancelled_experiments"]
        in_progress = len(self.active_experiments)
        pending = len(self.get_pending_experiments())

        progress_percentage = (completed + failed + cancelled) / total * 100 if total > 0 else 0

        return {
            "timestamp": datetime.now().isoformat(),
            "progress_percentage": progress_percentage,
            "total_experiments": total,
            "completed": completed,
            "failed": failed,
            "cancelled": cancelled,
            "in_progress": in_progress,
            "pending": pending,
            "success_rate": completed / (completed + failed) * 100 if (completed + failed) > 0 else 0,
            "estimated_remaining_time": self._estimate_remaining_time()
        }

    def _estimate_remaining_time(self) -> Optional[float]:
        """估算剩余时间"""
        # 计算平均执行时间
        completed_durations = []
        for experiment in self.experiments.values():
            if experiment["status"] == ExperimentStatus.COMPLETED and experiment["duration"]:
                completed_durations.append(experiment["duration"])

        if not completed_durations:
            return None

        avg_duration = sum(completed_durations) / len(completed_durations)
        remaining_experiments = len(self.get_pending_experiments()) + len(self.active_experiments)

        # 考虑并发执行
        concurrent_factor = min(remaining_experiments, self.max_concurrent_experiments)
        estimated_time = (remaining_experiments / concurrent_factor) * avg_duration

        return estimated_time
