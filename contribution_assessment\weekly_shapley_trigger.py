"""
周期性Shapley值计算触发器 (Weekly Shapley Trigger)

本模块实现了周期性Shapley值计算的触发机制，负责：
1. 监控交易周期完成情况
2. 自动触发Shapley值计算
3. 协调与OPRO系统的集成
4. 管理计算优先级和资源分配

主要功能：
- 检测7天交易周期的完成
- 自动触发联盟实验和Shapley值计算
- 与OPRO系统的无缝集成
- 资源管理和优先级控制

集成流程：
每日交易循环 → 周期检测 → Shapley分析触发 → 联盟实验执行 → 结果存储 → OPRO优化决策
"""

import json
import logging
import os
from typing import Dict, List, Any, Optional, Set, Tuple
from datetime import datetime, timedelta
from pathlib import Path
from enum import Enum
import itertools

from .enhanced_shapley_storage_manager import EnhancedShapleyStorageManager, ExperimentStatus
from .coalition_experiment_tracker import CoalitionExperimentTracker

class TriggerStatus(Enum):
    """触发器状态枚举"""
    IDLE = "IDLE"
    MONITORING = "MONITORING"
    TRIGGERED = "TRIGGERED"
    EXECUTING = "EXECUTING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"

class WeeklyShapleyTrigger:
    """
    周期性Shapley值计算触发器
    
    负责监控交易周期并自动触发Shapley值分析，包括：
    - 交易周期完成检测
    - 自动触发Shapley值计算
    - 与OPRO系统协调
    - 资源管理和优先级控制
    """
    
    def __init__(self,
                 config: Dict[str, Any],
                 storage_manager: Optional[EnhancedShapleyStorageManager] = None,
                 experiment_tracker: Optional[CoalitionExperimentTracker] = None,
                 logger: Optional[logging.Logger] = None):
        """
        初始化周期性Shapley触发器
        
        参数:
            config: 配置字典
            storage_manager: Shapley存储管理器
            experiment_tracker: 实验跟踪器
            logger: 日志记录器
        """
        self.config = config
        self.logger = logger or logging.getLogger(__name__)
        
        # 初始化组件
        self.storage_manager = storage_manager or EnhancedShapleyStorageManager(
            base_data_dir=config.get("data_dir", "data/trading"),
            config=config.get("shapley_analysis", {}),
            logger=self.logger
        )
        
        self.experiment_tracker = experiment_tracker or CoalitionExperimentTracker(
            config=config.get("shapley_analysis", {}),
            logger=self.logger
        )
        
        # 配置参数
        self.trading_days_per_week = config.get("trading_days_per_week", 5)
        self.shapley_analysis_enabled = config.get("shapley_analysis", {}).get("enabled", True)
        self.weekly_trigger_enabled = config.get("shapley_analysis", {}).get("weekly_trigger", True)
        self.coordinate_with_opro = config.get("opro_integration", {}).get("coordinate_with_opro", True)
        self.priority_level = config.get("opro_integration", {}).get("priority_level", "high")
        
        # 状态管理
        self.current_status = TriggerStatus.IDLE
        self.current_week_number = 0
        self.last_trigger_date = None
        self.active_analysis_id = None
        
        # 智能体配置
        self.all_agents = config.get("agents", {
            "analysis_agents": ["NAA", "TAA", "FAA"],
            "outlook_agents": ["BOA", "BeOA", "NOA"],
            "trading_agents": ["TRA"]
        })
        
        self.logger.info("周期性Shapley触发器初始化完成")
    
    def check_weekly_trigger_conditions(self, 
                                      current_date: Optional[datetime] = None,
                                      daily_results: Optional[Dict[str, Any]] = None) -> bool:
        """
        检查是否满足周期性触发条件
        
        参数:
            current_date: 当前日期
            daily_results: 每日交易结果
            
        返回:
            是否满足触发条件
        """
        if not self.shapley_analysis_enabled or not self.weekly_trigger_enabled:
            return False
        
        if current_date is None:
            current_date = datetime.now()
        
        # 检查是否已经在执行分析
        if self.current_status in [TriggerStatus.EXECUTING, TriggerStatus.TRIGGERED]:
            self.logger.info("Shapley分析正在执行中，跳过触发检查")
            return False
        
        # 检查是否达到周期天数
        if not self._check_trading_week_completion(current_date, daily_results):
            return False
        
        # 检查是否已经为当前周期触发过
        if self._is_week_already_analyzed(current_date):
            return False
        
        # 检查数据完整性
        if not self._check_data_completeness(current_date):
            self.logger.warning("交易数据不完整，延迟Shapley分析触发")
            return False
        
        self.logger.info(f"满足Shapley分析触发条件，当前日期: {current_date.date()}")
        return True
    
    def trigger_shapley_analysis(self, 
                                week_number: Optional[int] = None,
                                force_trigger: bool = False) -> Dict[str, Any]:
        """
        触发Shapley值分析
        
        参数:
            week_number: 周期编号
            force_trigger: 是否强制触发
            
        返回:
            触发结果字典
        """
        if not force_trigger and not self.shapley_analysis_enabled:
            return {"status": "disabled", "message": "Shapley分析已禁用"}
        
        if week_number is None:
            week_number = self._calculate_current_week_number()
        
        self.current_week_number = week_number
        self.current_status = TriggerStatus.TRIGGERED
        
        try:
            # 开始周期分析
            analysis_id = self.storage_manager.start_weekly_shapley_analysis(
                week_number=week_number,
                week_config={
                    "trigger_time": datetime.now().isoformat(),
                    "trigger_mode": "automatic" if not force_trigger else "manual",
                    "agents_config": self.all_agents
                }
            )
            
            self.active_analysis_id = analysis_id
            self.current_status = TriggerStatus.EXECUTING
            
            # 生成所有可能的联盟组合
            coalitions = self._generate_coalitions()
            
            # 初始化实验
            self.storage_manager.initialize_week_experiments(
                week_number=week_number,
                coalitions=coalitions,
                experiment_config={
                    "max_concurrent": self.config.get("shapley_analysis", {}).get("max_concurrent_experiments", 4),
                    "timeout": self.config.get("shapley_analysis", {}).get("experiment_timeout", 300)
                }
            )
            
            # 启动实验跟踪
            self.experiment_tracker.initialize_week_experiments(
                week_number=week_number,
                coalitions=coalitions
            )
            
            # 执行联盟实验
            execution_result = self._execute_coalition_experiments(week_number, coalitions)
            
            # 计算Shapley值
            shapley_results = self._calculate_shapley_values(week_number, execution_result)
            
            # 生成周期汇总
            weekly_summary = self.storage_manager.generate_weekly_summary(week_number)
            
            self.current_status = TriggerStatus.COMPLETED
            self.last_trigger_date = datetime.now()
            
            result = {
                "status": "success",
                "analysis_id": analysis_id,
                "week_number": week_number,
                "coalitions_tested": len(coalitions),
                "shapley_results": shapley_results,
                "weekly_summary": weekly_summary,
                "execution_time": (datetime.now() - datetime.fromisoformat(
                    weekly_summary.get("generation_time", datetime.now().isoformat())
                )).total_seconds()
            }
            
            self.logger.info(f"Shapley分析完成: week_{week_number}, 测试了{len(coalitions)}个联盟")
            return result
            
        except Exception as e:
            self.current_status = TriggerStatus.FAILED
            error_msg = f"Shapley分析执行失败: {str(e)}"
            self.logger.error(error_msg)
            
            return {
                "status": "failed",
                "error": error_msg,
                "week_number": week_number,
                "analysis_id": self.active_analysis_id
            }
    
    def coordinate_with_opro_cycle(self, 
                                 opro_assessor: Any,
                                 shapley_results: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        与OPRO优化周期协调
        
        参数:
            opro_assessor: OPRO评估器
            shapley_results: Shapley分析结果
            
        返回:
            协调结果
        """
        if not self.coordinate_with_opro:
            return {"status": "coordination_disabled"}
        
        try:
            coordination_result = {
                "coordination_time": datetime.now().isoformat(),
                "shapley_analysis_status": self.current_status.value,
                "recommendations": []
            }
            
            if shapley_results and "shapley_values" in shapley_results:
                # 基于Shapley值识别表现不佳的智能体
                underperforming_agents = self._identify_underperforming_agents(
                    shapley_results["shapley_values"]
                )
                
                coordination_result["underperforming_agents"] = underperforming_agents
                coordination_result["recommendations"].append({
                    "type": "optimization_targets",
                    "agents": underperforming_agents,
                    "reason": "基于Shapley值分析的低贡献度智能体"
                })
                
                # 建议OPRO优化策略
                if underperforming_agents:
                    coordination_result["recommendations"].append({
                        "type": "opro_strategy",
                        "strategy": "focused_optimization",
                        "target_agents": underperforming_agents,
                        "priority": self.priority_level
                    })
            
            self.logger.info("与OPRO系统协调完成")
            return coordination_result
            
        except Exception as e:
            error_msg = f"OPRO协调失败: {str(e)}"
            self.logger.error(error_msg)
            return {"status": "coordination_failed", "error": error_msg}
    
    def manage_computation_resources(self) -> Dict[str, Any]:
        """
        管理计算资源
        
        返回:
            资源管理状态
        """
        resource_status = {
            "timestamp": datetime.now().isoformat(),
            "current_status": self.current_status.value,
            "active_experiments": len(self.experiment_tracker.get_active_experiments()) if hasattr(self.experiment_tracker, 'get_active_experiments') else 0,
            "resource_usage": {
                "cpu_priority": self.priority_level,
                "max_concurrent_experiments": self.config.get("shapley_analysis", {}).get("max_concurrent_experiments", 4),
                "memory_limit": self.config.get("shapley_analysis", {}).get("memory_limit", "2GB")
            }
        }
        
        # 根据当前状态调整资源分配
        if self.current_status == TriggerStatus.EXECUTING:
            resource_status["recommendations"] = [
                "增加CPU优先级以加速联盟实验",
                "监控内存使用避免系统过载",
                "限制其他非关键任务的资源使用"
            ]
        
        return resource_status

    def _check_trading_week_completion(self,
                                     current_date: datetime,
                                     daily_results: Optional[Dict[str, Any]] = None) -> bool:
        """检查交易周期是否完成"""
        # 简单的周期检查：每5个交易日为一个周期
        # 实际实现中可能需要更复杂的逻辑来处理节假日等

        # 检查是否有足够的交易数据
        data_dir = Path(self.config.get("data_dir", "data/trading"))

        # 获取最近的交易日期
        recent_dates = []
        for item in data_dir.iterdir():
            if item.is_dir() and len(item.name) == 10:  # YYYY-MM-DD格式
                try:
                    date_obj = datetime.strptime(item.name, "%Y-%m-%d")
                    recent_dates.append(date_obj)
                except ValueError:
                    continue

        if len(recent_dates) < self.trading_days_per_week:
            return False

        # 检查最近的交易日是否构成完整的一周
        recent_dates.sort(reverse=True)
        latest_dates = recent_dates[:self.trading_days_per_week]

        # 检查日期连续性（允许周末间隔）
        date_span = (latest_dates[0] - latest_dates[-1]).days
        if date_span > 10:  # 超过10天认为不是连续的交易周
            return False

        return True

    def _is_week_already_analyzed(self, current_date: datetime) -> bool:
        """检查当前周期是否已经分析过"""
        week_number = self._calculate_current_week_number(current_date)

        shapley_dir = Path(self.config.get("data_dir", "data/trading")) / "shapley_analysis"
        week_dir = shapley_dir / f"week_{week_number}"

        if not week_dir.exists():
            return False

        # 检查是否有完整的分析结果
        required_files = ["week_summary.json", "experiment_status.json"]
        for file_name in required_files:
            if not (week_dir / file_name).exists():
                return False

        # 检查分析是否完成
        try:
            with open(week_dir / "experiment_status.json", 'r', encoding='utf-8') as f:
                status_data = json.load(f)
                summary = status_data.get("experiment_summary", {})
                total = summary.get("total_coalitions", 0)
                completed = summary.get("completed", 0)

                return total > 0 and completed == total
        except (json.JSONDecodeError, KeyError):
            return False

    def _check_data_completeness(self, current_date: datetime) -> bool:
        """检查交易数据完整性"""
        data_dir = Path(self.config.get("data_dir", "data/trading"))

        # 检查最近几天的数据是否完整
        for i in range(self.trading_days_per_week):
            check_date = current_date - timedelta(days=i)
            date_str = check_date.strftime("%Y-%m-%d")
            date_dir = data_dir / date_str

            if not date_dir.exists():
                continue

            # 检查是否有所有智能体的数据
            all_agents_list = []
            for agent_group in self.all_agents.values():
                all_agents_list.extend(agent_group)

            missing_agents = []
            for agent in all_agents_list:
                agent_dir = date_dir / agent
                if not agent_dir.exists():
                    missing_agents.append(agent)

            if missing_agents:
                self.logger.warning(f"日期 {date_str} 缺少智能体数据: {missing_agents}")
                return False

        return True

    def _calculate_current_week_number(self, current_date: Optional[datetime] = None) -> int:
        """计算当前周期编号"""
        if current_date is None:
            current_date = datetime.now()

        # 简单的周期计算：基于年份和周数
        year = current_date.year
        week_of_year = current_date.isocalendar()[1]

        # 生成唯一的周期编号
        week_number = (year - 2024) * 52 + week_of_year
        return max(1, week_number)

    def _generate_coalitions(self) -> List[Set[str]]:
        """生成所有可能的联盟组合"""
        all_agents_list = []
        for agent_group in self.all_agents.values():
            all_agents_list.extend(agent_group)

        coalitions = []

        # 生成不同大小的联盟组合
        for size in range(2, len(all_agents_list) + 1):
            for coalition in itertools.combinations(all_agents_list, size):
                coalitions.append(set(coalition))

        # 添加完整联盟
        coalitions.append(set(all_agents_list))

        self.logger.info(f"生成了 {len(coalitions)} 个联盟组合")
        return coalitions

    def _execute_coalition_experiments(self,
                                     week_number: int,
                                     coalitions: List[Set[str]]) -> Dict[str, Any]:
        """执行联盟实验"""
        execution_results = {}

        for i, coalition in enumerate(coalitions):
            coalition_name = "_".join(sorted(list(coalition)))

            try:
                self.logger.info(f"执行联盟实验 {i+1}/{len(coalitions)}: {coalition_name}")

                # 更新实验状态
                self.storage_manager.track_coalition_experiment(
                    coalition, ExperimentStatus.IN_PROGRESS
                )

                # 模拟联盟交易实验
                # 实际实现中这里会调用真实的交易模拟系统
                experiment_result = self._simulate_coalition_trading(coalition)

                # 保存实验结果
                self.storage_manager.save_coalition_experiment_result(
                    coalition=coalition,
                    result=experiment_result,
                    agent_decisions=experiment_result.get("agent_decisions", {}),
                    execution_logs=experiment_result.get("execution_logs", [])
                )

                execution_results[coalition_name] = experiment_result

                self.logger.info(f"联盟实验完成: {coalition_name}")

            except Exception as e:
                error_msg = f"联盟实验失败: {coalition_name}, 错误: {str(e)}"
                self.logger.error(error_msg)

                self.storage_manager.track_coalition_experiment(
                    coalition, ExperimentStatus.FAILED, error_msg
                )

                execution_results[coalition_name] = {"error": error_msg}

        return execution_results

    def _simulate_coalition_trading(self, coalition: Set[str]) -> Dict[str, Any]:
        """模拟联盟交易（占位符实现）"""
        # 这是一个占位符实现，实际中需要集成真实的交易模拟系统
        import random

        base_return = random.uniform(-0.1, 0.2)  # -10% to +20%
        volatility = random.uniform(0.05, 0.3)

        result = {
            "coalition": list(coalition),
            "simulation_time": datetime.now().isoformat(),
            "performance": {
                "total_return": base_return,
                "sharpe_ratio": base_return / volatility if volatility > 0 else 0,
                "max_drawdown": random.uniform(0.02, 0.15),
                "win_rate": random.uniform(0.4, 0.7)
            },
            "trading_summary": {
                "total_trades": random.randint(10, 50),
                "profitable_trades": random.randint(5, 35),
                "average_return_per_trade": base_return / random.randint(10, 50)
            },
            "agent_decisions": {
                agent: {
                    "decisions_made": random.randint(5, 20),
                    "accuracy": random.uniform(0.5, 0.9)
                } for agent in coalition
            },
            "execution_logs": [
                {
                    "timestamp": datetime.now().isoformat(),
                    "event": "coalition_simulation_completed",
                    "details": f"模拟联盟 {list(coalition)} 的交易表现"
                }
            ]
        }

        return result

    def _calculate_shapley_values(self,
                                week_number: int,
                                execution_results: Dict[str, Any]) -> Dict[str, Any]:
        """计算Shapley值"""
        # 这里应该集成真实的Shapley值计算逻辑
        # 目前提供一个简化的实现

        all_agents_list = []
        for agent_group in self.all_agents.values():
            all_agents_list.extend(agent_group)

        shapley_values = {}

        # 简化的Shapley值计算
        for agent in all_agents_list:
            agent_contributions = []

            # 计算包含该智能体的联盟的平均表现
            for coalition_name, result in execution_results.items():
                if "error" not in result and agent in result.get("coalition", []):
                    performance = result.get("performance", {})
                    agent_contributions.append(performance.get("total_return", 0.0))

            # 计算不包含该智能体的联盟的平均表现
            without_agent_contributions = []
            for coalition_name, result in execution_results.items():
                if "error" not in result and agent not in result.get("coalition", []):
                    performance = result.get("performance", {})
                    without_agent_contributions.append(performance.get("total_return", 0.0))

            # 简化的Shapley值：包含该智能体的联盟平均表现 - 不包含的联盟平均表现
            with_agent_avg = sum(agent_contributions) / len(agent_contributions) if agent_contributions else 0
            without_agent_avg = sum(without_agent_contributions) / len(without_agent_contributions) if without_agent_contributions else 0

            shapley_values[agent] = with_agent_avg - without_agent_avg

        shapley_results = {
            "week_number": week_number,
            "calculation_time": datetime.now().isoformat(),
            "shapley_values": shapley_values,
            "total_coalitions_analyzed": len([r for r in execution_results.values() if "error" not in r]),
            "calculation_method": "simplified_marginal_contribution"
        }

        # 保存Shapley结果
        week_dir = Path(self.config.get("data_dir", "data/trading")) / "shapley_analysis" / f"week_{week_number}"
        shapley_file = week_dir / "shapley_results.json"

        with open(shapley_file, 'w', encoding='utf-8') as f:
            json.dump(shapley_results, f, ensure_ascii=False, indent=2)

        self.logger.info(f"Shapley值计算完成，保存到: {shapley_file}")
        return shapley_results

    def _identify_underperforming_agents(self, shapley_values: Dict[str, float]) -> List[str]:
        """识别表现不佳的智能体"""
        if not shapley_values:
            return []

        # 计算平均Shapley值
        avg_shapley = sum(shapley_values.values()) / len(shapley_values)

        # 识别低于平均值的智能体
        underperforming = []
        for agent, value in shapley_values.items():
            if value < avg_shapley * 0.8:  # 低于平均值80%的智能体
                underperforming.append(agent)

        return underperforming
