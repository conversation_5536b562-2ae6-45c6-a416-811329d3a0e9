# 增强Shapley值计算系统使用指南

## 概述

增强Shapley值计算系统是对原有OPRO系统的重大升级，提供了更加系统化、组织化的Shapley值计算和数据管理功能。本系统通过联盟实验、并发处理和智能存储管理，大幅提升了多智能体贡献度评估的准确性和效率。

## 系统架构

### 核心组件

1. **EnhancedShapleyStorageManager** - 增强存储管理器
   - 负责数据组织和持久化存储
   - 管理周期性分析数据结构
   - 提供数据备份和恢复功能

2. **WeeklyShapleyTrigger** - 周期性触发器
   - 自动检测和触发Shapley分析
   - 与OPRO系统协调集成
   - 管理计算资源和执行调度

3. **CoalitionExperimentTracker** - 联盟实验跟踪器
   - 系统化管理联盟实验执行
   - 并发处理多个实验任务
   - 提供重试机制和错误恢复

### 数据组织结构

```
data/trading/shapley_analysis/
├── week_1/
│   ├── coalition_NAA_TAA/
│   │   ├── experiment_config.json
│   │   ├── execution_result.json
│   │   ├── agent_decisions/
│   │   └── execution_logs/
│   ├── coalition_NAA_TAA_FAA/
│   └── weekly_summary.json
├── week_2/
└── system_config.json
```

## 安装和配置

### 系统要求

- Python 3.8+
- 现有OPRO系统环境
- 足够的磁盘空间用于存储实验数据

### 配置文件

创建或更新系统配置文件：

```json
{
  "data_dir": "data/trading",
  "shapley_analysis": {
    "enabled": true,
    "weekly_trigger": true,
    "max_concurrent_experiments": 4,
    "experiment_timeout": 300,
    "auto_retry_failed": true,
    "max_retry_attempts": 3,
    "retry_delay": 10
  },
  "opro_integration": {
    "coordinate_with_opro": true,
    "priority_level": "high"
  },
  "agents": {
    "analysis_agents": ["NAA", "TAA", "FAA"],
    "outlook_agents": ["BOA", "BeOA", "NOA"],
    "trading_agents": ["TRA"]
  },
  "trading_days_per_week": 5
}
```

## 使用方法

### 1. 基本使用

#### 运行完整OPRO系统（推荐）

```bash
# 使用增强Shapley系统运行OPRO
python run_opro_system.py --provider zhipuai --mode optimization

# 运行集成模式（评估+优化）
python run_opro_system.py --provider zhipuai --mode integrated
```

#### 独立运行Shapley分析

```python
from contribution_assessment.weekly_shapley_trigger import WeeklyShapleyTrigger

# 初始化触发器
config = {
    "data_dir": "data/trading",
    "shapley_analysis": {"enabled": True, "weekly_trigger": True},
    "agents": {"analysis_agents": ["NAA", "TAA", "FAA"]}
}

trigger = WeeklyShapleyTrigger(config=config)

# 触发Shapley分析
result = trigger.trigger_shapley_analysis(week_number=1, force_trigger=True)
print(f"分析结果: {result}")
```

### 2. 高级配置

#### 自定义联盟生成

```python
from contribution_assessment.enhanced_shapley_storage_manager import EnhancedShapleyStorageManager

storage_manager = EnhancedShapleyStorageManager(base_data_dir="data/trading")

# 自定义联盟
custom_coalitions = [
    {"NAA", "TAA"},
    {"NAA", "TAA", "FAA"},
    {"BOA", "BeOA", "NOA"},
    {"NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"}
]

# 初始化实验
storage_manager.initialize_week_experiments(
    week_number=1,
    coalitions=custom_coalitions,
    experiment_config={"custom_mode": True}
)
```

#### 并发实验控制

```python
from contribution_assessment.coalition_experiment_tracker import CoalitionExperimentTracker

# 配置并发参数
config = {
    "max_concurrent_experiments": 6,  # 最大并发数
    "experiment_timeout": 600,        # 超时时间（秒）
    "max_retry_attempts": 5           # 最大重试次数
}

tracker = CoalitionExperimentTracker(config=config)
```

### 3. 监控和调试

#### 查看实验状态

```python
# 获取当前实验状态
status = tracker.get_experiment_status()
print(f"总实验数: {status['statistics']['total_experiments']}")
print(f"完成实验数: {status['statistics']['completed_experiments']}")
print(f"失败实验数: {status['statistics']['failed_experiments']}")
```

#### 生成执行报告

```python
# 生成详细执行报告
report = tracker.generate_execution_report()
print(f"执行摘要: {report['execution_summary']}")
print(f"性能指标: {report['performance_metrics']}")
```

#### 查看周期汇总

```python
# 获取周期汇总数据
weekly_summary = storage_manager.generate_weekly_summary(week_number=1)
print(f"Shapley值: {weekly_summary['shapley_values']}")
print(f"最佳联盟: {weekly_summary['best_coalition']}")
```

## 测试和验证

### 运行测试套件

```bash
# 运行完整测试套件
python test_enhanced_shapley_system.py --test-all --verbose

# 运行特定组件测试
python test_enhanced_shapley_system.py --test-storage --verbose
python test_enhanced_shapley_system.py --test-tracker --verbose
python test_enhanced_shapley_system.py --test-trigger --verbose

# 测试OPRO集成
python test_enhanced_shapley_system.py --test-integration --verbose

# 保存测试结果
python test_enhanced_shapley_system.py --test-all --output test_results.json
```

### 验证数据完整性

```bash
# 验证数据结构
python test_enhanced_shapley_system.py --test-data-structure --verbose
```

## 性能优化

### 1. 并发优化

- **调整并发数**: 根据系统资源调整 `max_concurrent_experiments`
- **超时设置**: 合理设置 `experiment_timeout` 避免长时间等待
- **资源监控**: 启用资源监控避免系统过载

### 2. 存储优化

- **数据压缩**: 启用JSON数据压缩减少存储空间
- **定期清理**: 设置数据保留策略清理过期数据
- **备份策略**: 配置自动备份重要分析结果

### 3. 内存优化

```python
# 配置内存优化参数
config = {
    "shapley_analysis": {
        "batch_size": 100,           # 批处理大小
        "memory_limit": "2GB",       # 内存限制
        "enable_gc": True            # 启用垃圾回收
    }
}
```

## 故障排除

### 常见问题

#### 1. 导入错误
```
ImportError: No module named 'contribution_assessment.enhanced_shapley_storage_manager'
```
**解决方案**: 确保所有新组件文件已正确放置在 `contribution_assessment/` 目录中。

#### 2. 权限错误
```
PermissionError: [Errno 13] Permission denied: 'data/trading/shapley_analysis'
```
**解决方案**: 检查数据目录权限，确保Python进程有读写权限。

#### 3. 内存不足
```
MemoryError: Unable to allocate array
```
**解决方案**: 减少 `max_concurrent_experiments` 或增加系统内存。

#### 4. 超时错误
```
TimeoutError: Experiment execution timeout
```
**解决方案**: 增加 `experiment_timeout` 值或优化实验执行逻辑。

### 日志分析

启用详细日志进行问题诊断：

```python
import logging

# 设置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('shapley_system.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
```

### 数据恢复

如果数据损坏，可以使用备份恢复：

```python
# 恢复数据
storage_manager.restore_from_backup(
    backup_path="data/trading/backups/week_1_backup.json",
    target_week=1
)
```

## 最佳实践

### 1. 定期维护

- 每周检查系统状态和性能指标
- 定期清理过期的实验数据
- 监控磁盘空间使用情况

### 2. 配置管理

- 使用版本控制管理配置文件
- 为不同环境维护不同的配置
- 定期备份重要配置

### 3. 监控告警

- 设置实验失败率告警
- 监控系统资源使用情况
- 配置关键指标阈值告警

### 4. 数据安全

- 定期备份重要分析结果
- 实施数据访问控制
- 加密敏感配置信息

## 升级和迁移

### 从旧系统迁移

1. **备份现有数据**
```bash
cp -r data/trading data/trading_backup_$(date +%Y%m%d)
```

2. **运行迁移脚本**
```python
from contribution_assessment.enhanced_shapley_storage_manager import EnhancedShapleyStorageManager

# 迁移历史数据
storage_manager = EnhancedShapleyStorageManager()
storage_manager.migrate_legacy_data("data/trading_backup")
```

3. **验证迁移结果**
```bash
python test_enhanced_shapley_system.py --test-data-structure
```

## 技术支持

如遇到问题，请：

1. 查看系统日志文件
2. 运行诊断测试脚本
3. 检查配置文件格式
4. 验证系统依赖完整性

更多技术细节请参考系统设计文档 `IMPROVED_SHAPLEY_SYSTEM_DESIGN.md`。
