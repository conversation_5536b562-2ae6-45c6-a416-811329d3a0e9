#!/usr/bin/env python3
"""
增强Shapley值计算系统测试脚本 (Enhanced Shapley System Test Script)

本脚本用于测试和验证新的增强Shapley值计算系统，包括：
1. 存储管理器功能测试
2. 联盟实验跟踪测试
3. 周期性触发机制测试
4. OPRO系统集成测试

使用示例:
    # 运行完整测试套件
    python test_enhanced_shapley_system.py --test-all
    
    # 测试存储管理器
    python test_enhanced_shapley_system.py --test-storage
    
    # 测试实验跟踪器
    python test_enhanced_shapley_system.py --test-tracker
    
    # 测试触发机制
    python test_enhanced_shapley_system.py --test-trigger
    
    # 测试OPRO集成
    python test_enhanced_shapley_system.py --test-integration
"""

import argparse
import json
import logging
import os
import sys
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, List, Set
import tempfile
import shutil

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_logging(verbose: bool = False) -> logging.Logger:
    """设置日志记录"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def create_test_config() -> Dict[str, Any]:
    """创建测试配置"""
    return {
        "data_dir": "test_data/trading",
        "shapley_analysis": {
            "enabled": True,
            "weekly_trigger": True,
            "max_concurrent_experiments": 2,
            "experiment_timeout": 60,
            "auto_retry_failed": True,
            "max_retry_attempts": 2,
            "retry_delay": 5
        },
        "opro_integration": {
            "coordinate_with_opro": True,
            "priority_level": "high"
        },
        "agents": {
            "analysis_agents": ["NAA", "TAA", "FAA"],
            "outlook_agents": ["BOA", "BeOA", "NOA"],
            "trading_agents": ["TRA"]
        },
        "trading_days_per_week": 5
    }

def create_test_data_structure(base_dir: str, logger: logging.Logger) -> None:
    """创建测试数据结构"""
    logger.info("创建测试数据结构...")
    
    base_path = Path(base_dir)
    base_path.mkdir(parents=True, exist_ok=True)
    
    # 创建模拟的交易日期数据
    test_dates = [
        "2025-01-01", "2025-01-02", "2025-01-03", "2025-01-06", "2025-01-07",  # Week 1
        "2025-01-08", "2025-01-09", "2025-01-10", "2025-01-13", "2025-01-14"   # Week 2
    ]
    
    agents = ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"]
    
    for date in test_dates:
        date_dir = base_path / date
        date_dir.mkdir(exist_ok=True)
        
        for agent in agents:
            agent_dir = date_dir / agent
            agent_dir.mkdir(exist_ok=True)
            
            # 创建模拟的智能体数据文件
            (agent_dir / "inputs.json").write_text(
                json.dumps({"test_input": f"data for {agent} on {date}"}, ensure_ascii=False, indent=2),
                encoding='utf-8'
            )
            (agent_dir / "outputs.json").write_text(
                json.dumps({"test_output": f"result for {agent} on {date}"}, ensure_ascii=False, indent=2),
                encoding='utf-8'
            )
    
    logger.info(f"测试数据结构创建完成: {len(test_dates)} 个交易日, {len(agents)} 个智能体")

def test_storage_manager(config: Dict[str, Any], logger: logging.Logger) -> Dict[str, Any]:
    """测试存储管理器"""
    logger.info("=" * 60)
    logger.info("测试存储管理器功能")
    logger.info("=" * 60)
    
    try:
        from contribution_assessment.enhanced_shapley_storage_manager import EnhancedShapleyStorageManager, ExperimentStatus
        
        # 初始化存储管理器
        storage_manager = EnhancedShapleyStorageManager(
            base_data_dir=config["data_dir"],
            config=config["shapley_analysis"],
            logger=logger
        )
        
        test_results = {
            "test_name": "storage_manager",
            "success": True,
            "tests": {}
        }
        
        # 测试1: 开始周期分析
        logger.info("测试1: 开始周期分析...")
        analysis_id = storage_manager.start_weekly_shapley_analysis(
            week_number=1,
            week_config={
                "test_mode": True,
                "agents": config["agents"]
            }
        )
        test_results["tests"]["start_analysis"] = {
            "success": analysis_id is not None,
            "analysis_id": analysis_id
        }
        logger.info(f"✅ 周期分析开始成功, ID: {analysis_id}")
        
        # 测试2: 初始化实验
        logger.info("测试2: 初始化联盟实验...")
        test_coalitions = [
            {"NAA", "TAA"},
            {"NAA", "TAA", "FAA"},
            {"BOA", "BeOA"},
            {"NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"}
        ]
        
        storage_manager.initialize_week_experiments(
            week_number=1,
            coalitions=test_coalitions,
            experiment_config={"test_mode": True}
        )
        test_results["tests"]["initialize_experiments"] = {
            "success": True,
            "coalitions_count": len(test_coalitions)
        }
        logger.info(f"✅ 联盟实验初始化成功, {len(test_coalitions)} 个联盟")
        
        # 测试3: 跟踪实验状态
        logger.info("测试3: 跟踪实验状态...")
        test_coalition = test_coalitions[0]
        storage_manager.track_coalition_experiment(test_coalition, ExperimentStatus.IN_PROGRESS)
        storage_manager.track_coalition_experiment(test_coalition, ExperimentStatus.COMPLETED)
        test_results["tests"]["track_experiments"] = {"success": True}
        logger.info("✅ 实验状态跟踪成功")
        
        # 测试4: 保存实验结果
        logger.info("测试4: 保存实验结果...")
        test_result = {
            "coalition": list(test_coalition),
            "performance": {
                "total_return": 0.15,
                "sharpe_ratio": 1.2,
                "max_drawdown": 0.05,
                "win_rate": 0.65
            },
            "execution_time": 45.2
        }
        
        storage_manager.save_coalition_experiment_result(
            coalition=test_coalition,
            result=test_result,
            agent_decisions={"NAA": {"decisions": 10}, "TAA": {"decisions": 8}},
            execution_logs=[{"timestamp": datetime.now().isoformat(), "event": "test_completed"}]
        )
        test_results["tests"]["save_results"] = {"success": True}
        logger.info("✅ 实验结果保存成功")
        
        # 测试5: 生成周期汇总
        logger.info("测试5: 生成周期汇总...")
        weekly_summary = storage_manager.generate_weekly_summary(1)
        test_results["tests"]["weekly_summary"] = {
            "success": weekly_summary is not None,
            "summary_keys": list(weekly_summary.keys()) if weekly_summary else []
        }
        logger.info("✅ 周期汇总生成成功")
        
        logger.info("存储管理器测试完成 ✅")
        return test_results
        
    except Exception as e:
        logger.error(f"存储管理器测试失败: {e}")
        return {
            "test_name": "storage_manager",
            "success": False,
            "error": str(e)
        }

def test_experiment_tracker(config: Dict[str, Any], logger: logging.Logger) -> Dict[str, Any]:
    """测试实验跟踪器"""
    logger.info("=" * 60)
    logger.info("测试实验跟踪器功能")
    logger.info("=" * 60)
    
    try:
        from contribution_assessment.coalition_experiment_tracker import CoalitionExperimentTracker
        
        # 初始化实验跟踪器
        tracker = CoalitionExperimentTracker(
            config=config["shapley_analysis"],
            logger=logger
        )
        
        test_results = {
            "test_name": "experiment_tracker",
            "success": True,
            "tests": {}
        }
        
        # 测试1: 初始化周期实验
        logger.info("测试1: 初始化周期实验...")
        test_coalitions = [
            {"NAA", "TAA"},
            {"BOA", "BeOA"},
            {"NAA", "TAA", "FAA"}
        ]
        
        tracker.initialize_week_experiments(
            week_number=1,
            coalitions=test_coalitions,
            experiment_config={"test_mode": True}
        )
        test_results["tests"]["initialize_week"] = {
            "success": True,
            "coalitions_count": len(test_coalitions)
        }
        logger.info(f"✅ 周期实验初始化成功, {len(test_coalitions)} 个联盟")
        
        # 测试2: 获取实验状态
        logger.info("测试2: 获取实验状态...")
        status = tracker.get_experiment_status()
        test_results["tests"]["get_status"] = {
            "success": status is not None,
            "total_experiments": status.get("statistics", {}).get("total_experiments", 0)
        }
        logger.info("✅ 实验状态获取成功")
        
        # 测试3: 生成执行报告
        logger.info("测试3: 生成执行报告...")
        report = tracker.generate_execution_report()
        test_results["tests"]["execution_report"] = {
            "success": report is not None,
            "report_keys": list(report.keys()) if report else []
        }
        logger.info("✅ 执行报告生成成功")
        
        logger.info("实验跟踪器测试完成 ✅")
        return test_results
        
    except Exception as e:
        logger.error(f"实验跟踪器测试失败: {e}")
        return {
            "test_name": "experiment_tracker",
            "success": False,
            "error": str(e)
        }

def test_shapley_trigger(config: Dict[str, Any], logger: logging.Logger) -> Dict[str, Any]:
    """测试Shapley触发机制"""
    logger.info("=" * 60)
    logger.info("测试Shapley触发机制")
    logger.info("=" * 60)
    
    try:
        from contribution_assessment.weekly_shapley_trigger import WeeklyShapleyTrigger
        
        # 初始化触发器
        trigger = WeeklyShapleyTrigger(
            config=config,
            logger=logger
        )
        
        test_results = {
            "test_name": "shapley_trigger",
            "success": True,
            "tests": {}
        }
        
        # 测试1: 检查触发条件
        logger.info("测试1: 检查触发条件...")
        current_date = datetime.now()
        trigger_conditions = trigger.check_weekly_trigger_conditions(
            current_date=current_date,
            daily_results={"test": "data"}
        )
        test_results["tests"]["trigger_conditions"] = {
            "success": True,
            "conditions_met": trigger_conditions
        }
        logger.info(f"✅ 触发条件检查完成: {trigger_conditions}")
        
        # 测试2: 强制触发分析
        logger.info("测试2: 强制触发Shapley分析...")
        trigger_result = trigger.trigger_shapley_analysis(
            week_number=1,
            force_trigger=True
        )
        test_results["tests"]["trigger_analysis"] = {
            "success": trigger_result.get("status") in ["success", "failed"],  # 允许失败，因为是测试环境
            "status": trigger_result.get("status"),
            "week_number": trigger_result.get("week_number")
        }
        logger.info(f"✅ Shapley分析触发完成: {trigger_result.get('status')}")
        
        # 测试3: 资源管理
        logger.info("测试3: 资源管理...")
        resource_status = trigger.manage_computation_resources()
        test_results["tests"]["resource_management"] = {
            "success": resource_status is not None,
            "current_status": resource_status.get("current_status")
        }
        logger.info("✅ 资源管理测试完成")
        
        logger.info("Shapley触发机制测试完成 ✅")
        return test_results
        
    except Exception as e:
        logger.error(f"Shapley触发机制测试失败: {e}")
        return {
            "test_name": "shapley_trigger",
            "success": False,
            "error": str(e)
        }

def test_opro_integration(config: Dict[str, Any], logger: logging.Logger) -> Dict[str, Any]:
    """测试OPRO系统集成"""
    logger.info("=" * 60)
    logger.info("测试OPRO系统集成")
    logger.info("=" * 60)

    try:
        # 模拟OPRO集成测试
        test_results = {
            "test_name": "opro_integration",
            "success": True,
            "tests": {}
        }

        # 测试1: 配置验证
        logger.info("测试1: OPRO集成配置验证...")
        opro_config = config.get("opro_integration", {})
        test_results["tests"]["config_validation"] = {
            "success": True,
            "coordinate_with_opro": opro_config.get("coordinate_with_opro", False),
            "priority_level": opro_config.get("priority_level", "medium")
        }
        logger.info("✅ OPRO集成配置验证完成")

        # 测试2: 模拟协调流程
        logger.info("测试2: 模拟OPRO协调流程...")
        from contribution_assessment.weekly_shapley_trigger import WeeklyShapleyTrigger

        trigger = WeeklyShapleyTrigger(config=config, logger=logger)

        # 模拟Shapley结果
        mock_shapley_results = {
            "shapley_values": {
                "NAA": 0.15,
                "TAA": 0.12,
                "FAA": 0.08,  # 表现较差
                "BOA": 0.14,
                "BeOA": 0.13,
                "NOA": 0.09,  # 表现较差
                "TRA": 0.16
            }
        }

        coordination_result = trigger.coordinate_with_opro_cycle(
            opro_assessor=None,  # 模拟环境中为None
            shapley_results=mock_shapley_results
        )

        test_results["tests"]["coordination"] = {
            "success": coordination_result is not None,
            "underperforming_agents": coordination_result.get("underperforming_agents", []),
            "recommendations_count": len(coordination_result.get("recommendations", []))
        }
        logger.info("✅ OPRO协调流程测试完成")

        logger.info("OPRO系统集成测试完成 ✅")
        return test_results

    except Exception as e:
        logger.error(f"OPRO系统集成测试失败: {e}")
        return {
            "test_name": "opro_integration",
            "success": False,
            "error": str(e)
        }

def test_data_structure_validation(config: Dict[str, Any], logger: logging.Logger) -> Dict[str, Any]:
    """测试数据结构验证"""
    logger.info("=" * 60)
    logger.info("测试数据结构验证")
    logger.info("=" * 60)

    try:
        test_results = {
            "test_name": "data_structure_validation",
            "success": True,
            "tests": {}
        }

        # 测试1: 目录结构验证
        logger.info("测试1: 目录结构验证...")
        base_dir = Path(config["data_dir"])

        # 检查基础目录
        if base_dir.exists():
            test_results["tests"]["base_directory"] = {"success": True, "path": str(base_dir)}
            logger.info(f"✅ 基础目录存在: {base_dir}")
        else:
            test_results["tests"]["base_directory"] = {"success": False, "path": str(base_dir)}
            logger.warning(f"⚠️  基础目录不存在: {base_dir}")

        # 检查Shapley分析目录
        shapley_dir = base_dir / "shapley_analysis"
        if shapley_dir.exists():
            week_dirs = [d for d in shapley_dir.iterdir() if d.is_dir() and d.name.startswith("week_")]
            test_results["tests"]["shapley_directory"] = {
                "success": True,
                "path": str(shapley_dir),
                "week_directories": len(week_dirs)
            }
            logger.info(f"✅ Shapley分析目录存在: {shapley_dir}, 包含 {len(week_dirs)} 个周期目录")
        else:
            test_results["tests"]["shapley_directory"] = {"success": False, "path": str(shapley_dir)}
            logger.info(f"ℹ️  Shapley分析目录不存在（正常，首次运行）: {shapley_dir}")

        # 测试2: 文件格式验证
        logger.info("测试2: 文件格式验证...")
        valid_files = 0
        total_files = 0

        for date_dir in base_dir.iterdir():
            if date_dir.is_dir() and len(date_dir.name) == 10:  # YYYY-MM-DD格式
                for agent_dir in date_dir.iterdir():
                    if agent_dir.is_dir():
                        for file_path in agent_dir.glob("*.json"):
                            total_files += 1
                            try:
                                with open(file_path, 'r', encoding='utf-8') as f:
                                    json.load(f)
                                valid_files += 1
                            except json.JSONDecodeError:
                                logger.warning(f"⚠️  无效JSON文件: {file_path}")

        test_results["tests"]["file_format"] = {
            "success": valid_files == total_files,
            "valid_files": valid_files,
            "total_files": total_files
        }
        logger.info(f"✅ 文件格式验证完成: {valid_files}/{total_files} 个有效JSON文件")

        logger.info("数据结构验证测试完成 ✅")
        return test_results

    except Exception as e:
        logger.error(f"数据结构验证测试失败: {e}")
        return {
            "test_name": "data_structure_validation",
            "success": False,
            "error": str(e)
        }

def run_all_tests(config: Dict[str, Any], logger: logging.Logger) -> Dict[str, Any]:
    """运行所有测试"""
    logger.info("=" * 80)
    logger.info("开始运行增强Shapley值计算系统完整测试套件")
    logger.info("=" * 80)

    start_time = time.time()
    all_results = {
        "test_suite": "enhanced_shapley_system",
        "start_time": datetime.now().isoformat(),
        "config": config,
        "results": []
    }

    # 运行各项测试
    tests = [
        ("数据结构验证", test_data_structure_validation),
        ("存储管理器", test_storage_manager),
        ("实验跟踪器", test_experiment_tracker),
        ("Shapley触发机制", test_shapley_trigger),
        ("OPRO系统集成", test_opro_integration)
    ]

    successful_tests = 0

    for test_name, test_func in tests:
        logger.info(f"\n开始测试: {test_name}")
        try:
            result = test_func(config, logger)
            all_results["results"].append(result)

            if result.get("success", False):
                successful_tests += 1
                logger.info(f"✅ {test_name} 测试通过")
            else:
                logger.error(f"❌ {test_name} 测试失败: {result.get('error', '未知错误')}")

        except Exception as e:
            logger.error(f"❌ {test_name} 测试执行异常: {e}")
            all_results["results"].append({
                "test_name": test_name.lower().replace(" ", "_"),
                "success": False,
                "error": f"测试执行异常: {str(e)}"
            })

    # 计算总体结果
    total_time = time.time() - start_time
    all_results.update({
        "end_time": datetime.now().isoformat(),
        "total_execution_time": total_time,
        "total_tests": len(tests),
        "successful_tests": successful_tests,
        "failed_tests": len(tests) - successful_tests,
        "success_rate": successful_tests / len(tests) * 100,
        "overall_success": successful_tests == len(tests)
    })

    # 输出测试摘要
    logger.info("=" * 80)
    logger.info("测试套件执行完成")
    logger.info("=" * 80)
    logger.info(f"总测试数: {len(tests)}")
    logger.info(f"成功测试: {successful_tests}")
    logger.info(f"失败测试: {len(tests) - successful_tests}")
    logger.info(f"成功率: {successful_tests / len(tests) * 100:.1f}%")
    logger.info(f"总执行时间: {total_time:.2f}秒")

    if all_results["overall_success"]:
        logger.info("🎉 所有测试通过！增强Shapley值计算系统功能正常")
    else:
        logger.warning("⚠️  部分测试失败，请检查系统配置和依赖")

    return all_results

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="增强Shapley值计算系统测试脚本")

    parser.add_argument("--test-all", action="store_true", help="运行所有测试")
    parser.add_argument("--test-storage", action="store_true", help="测试存储管理器")
    parser.add_argument("--test-tracker", action="store_true", help="测试实验跟踪器")
    parser.add_argument("--test-trigger", action="store_true", help="测试触发机制")
    parser.add_argument("--test-integration", action="store_true", help="测试OPRO集成")
    parser.add_argument("--test-data-structure", action="store_true", help="测试数据结构")
    parser.add_argument("--verbose", action="store_true", help="详细日志")
    parser.add_argument("--output", type=str, help="结果输出文件")
    parser.add_argument("--cleanup", action="store_true", help="测试后清理临时数据")

    args = parser.parse_args()

    # 设置日志
    logger = setup_logging(args.verbose)

    # 创建测试配置
    config = create_test_config()

    # 创建临时测试目录
    temp_dir = None
    if args.cleanup:
        temp_dir = tempfile.mkdtemp(prefix="shapley_test_")
        config["data_dir"] = temp_dir
        logger.info(f"使用临时测试目录: {temp_dir}")

    try:
        # 创建测试数据结构
        create_test_data_structure(config["data_dir"], logger)

        # 根据参数运行相应测试
        if args.test_all:
            result = run_all_tests(config, logger)
        elif args.test_storage:
            result = test_storage_manager(config, logger)
        elif args.test_tracker:
            result = test_experiment_tracker(config, logger)
        elif args.test_trigger:
            result = test_shapley_trigger(config, logger)
        elif args.test_integration:
            result = test_opro_integration(config, logger)
        elif args.test_data_structure:
            result = test_data_structure_validation(config, logger)
        else:
            logger.info("未指定测试类型，运行完整测试套件")
            result = run_all_tests(config, logger)

        # 输出结果
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            logger.info(f"测试结果已保存到: {args.output}")

        # 返回适当的退出码
        if result.get("overall_success", result.get("success", False)):
            sys.exit(0)
        else:
            sys.exit(1)

    except Exception as e:
        logger.error(f"测试执行失败: {e}")
        sys.exit(1)

    finally:
        # 清理临时目录
        if temp_dir and args.cleanup and os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir)
                logger.info(f"临时测试目录已清理: {temp_dir}")
            except Exception as e:
                logger.warning(f"清理临时目录失败: {e}")

if __name__ == "__main__":
    main()
